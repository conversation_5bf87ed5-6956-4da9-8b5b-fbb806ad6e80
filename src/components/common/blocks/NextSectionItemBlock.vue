<template>
  <div class="next-section-block">
    <!-- Section Navigation Label -->
    <div class="row items-center q-mb-md">
      <q-icon name="arrow_forward" color="primary" size="sm" class="q-mr-sm" />
      <span class="text-subtitle2 text-primary">หลังจากส่วนนี้</span>
    </div>

    <!-- Section Navigation Dropdown -->
    <div class="row items-center">
      <q-select
        :model-value="currentNextSection"
        :options="sectionDropdownOptions"
        emit-value
        map-options
        outlined
        dense
        placeholder="ดำเนินการต่อไปยังส่วนถัดไป"
        style="min-width: 300px"
        @update:model-value="handleSectionSelect"
        :loading="isSaving"
        :disable="isSaving"
      >
        <template #selected>
          <span v-if="currentNextSection">{{ getSelectedSectionLabel() }}</span>
          <span v-else class="text-grey-6">ดำเนินการต่อไปยังส่วนถัดไป</span>
        </template>
        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>
        <template #loading>
          <q-spinner-dots color="primary" />
        </template>
      </q-select>
      
      <!-- Save indicator -->
      <q-spinner-dots v-if="isSaving" color="primary" class="q-ml-sm" />
      <q-icon v-else-if="showSaveSuccess" name="check_circle" color="positive" class="q-ml-sm" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject, watch } from 'vue';
import type { ItemBlock } from 'src/types/models';

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
  showSectionDropdowns?: boolean;
  availableSections?: { label: string; value: number; headerTitle?: string }[];
}>();

const emit = defineEmits<{
  'update:option-next-section': [optionId: number, nextSection: number | null];
}>();

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  isSaving: { value: boolean };
}>('autoSave');

// Local state
const isSaving = ref(false);
const showSaveSuccess = ref(false);

// Get the current nextSection value from the first option (NEXTSECTION blocks should have exactly one option)
const currentNextSection = computed(() => {
  const firstOption = props.itemBlock.options?.[0];
  if (!firstOption) return null;
  
  const nextSection = firstOption.nextSection;
  if (nextSection === null || nextSection === undefined || nextSection === 0) {
    return null;
  }
  
  return nextSection;
});

// Computed property for section dropdown options with Thai formatting
const sectionDropdownOptions = computed(() => {
  if (!props.availableSections || props.availableSections.length === 0) return [];

  // Add default option first
  const options: { label: string; value: number | null }[] = [
    {
      label: 'ดำเนินการต่อไปยังส่วนถัดไป',
      value: null,
    },
  ];

  // Add section options with Thai formatting
  props.availableSections.forEach((section) => {
    const headerTitle = section.headerTitle || '';
    const label = headerTitle
      ? `ไปที่ส่วนที่ ${section.value} (${headerTitle})`
      : `ไปที่ส่วนที่ ${section.value}`;

    options.push({
      label: label,
      value: section.value,
    });
  });

  return options;
});

// Helper function to get the selected section label for display
function getSelectedSectionLabel(): string {
  const nextSection = currentNextSection.value;
  if (!nextSection) return 'ดำเนินการต่อไปยังส่วนถัดไป';

  const section = props.availableSections?.find((s) => s.value === nextSection);
  if (!section) return `ไปที่ส่วนที่ ${nextSection}`;

  const headerTitle = section.headerTitle || '';
  return headerTitle
    ? `ไปที่ส่วนที่ ${section.value} (${headerTitle})`
    : `ไปที่ส่วนที่ ${section.value}`;
}

// Handler for section selection
async function handleSectionSelect(sectionValue: number | null) {
  const firstOption = props.itemBlock.options?.[0];
  if (!firstOption?.id) {
    console.error('❌ [NEXTSECTION] No option found for NEXTSECTION block');
    return;
  }

  console.log('🔄 [NEXTSECTION] Handling section selection:', {
    optionId: firstOption.id,
    sectionValue,
    sectionValueType: typeof sectionValue,
    isNull: sectionValue === null,
    currentValue: currentNextSection.value,
  });

  try {
    isSaving.value = true;
    showSaveSuccess.value = false;

    // Emit the update to parent component
    emit('update:option-next-section', firstOption.id, sectionValue);

    // Show success indicator briefly
    setTimeout(() => {
      showSaveSuccess.value = true;
      setTimeout(() => {
        showSaveSuccess.value = false;
      }, 2000);
    }, 500);
  } catch (error) {
    console.error('❌ [NEXTSECTION] Error updating section:', error);
  } finally {
    setTimeout(() => {
      isSaving.value = false;
    }, 800);
  }
}

// Watch for external saving state changes
watch(
  () => autoSave?.isSaving?.value,
  (newValue) => {
    if (newValue !== undefined) {
      isSaving.value = newValue;
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.next-section-block {
  padding: 16px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
  margin: 8px 0;
}

.next-section-block:hover {
  border-color: #c0c0c0;
  background-color: #f5f5f5;
}
</style>
